# 操作注册机制使用文档

## 概述

采用装饰器注册机制优化操作映射表，解决了所有业务操作都集中在 `tasks/proxy.py` 中管理的问题。现在每个业务模块可以独立注册自己的操作。

## 核心文件

- `tasks/registry.py` - 操作注册器
- `tasks/proxy.py` - 优化后的代理任务（支持装饰器注册和手动注册）
- `business/patient_business.py` - 使用装饰器注册的示例

## 使用方法

### 1. 在业务模块中注册操作

```python
# business/your_business.py
from tasks.registry import register_operation
from models import Session

class YourBusiness:
    def __init__(self):
        self.session = Session()
    
    @register_operation('your_module.your_operation')
    def your_operation(self, param1, param2):
        """你的业务逻辑"""
        try:
            # 业务逻辑代码
            result = self.do_something(param1, param2)
            return {
                'success': True,
                'data': result
            }
        except Exception as e:
            return {
                'success': False,
                'message': str(e)
            }

# 创建业务实例（重要：必须创建实例才能注册）
your_business = YourBusiness()
```

### 2. 调用注册的操作

```python
# 在其他地方调用
from tasks.proxy import execute

# 异步调用
result = execute.delay('your_module.your_operation', args=[param1, param2])
print(result.get())

# 或者使用关键字参数
result = execute.delay('your_module.your_operation', kwargs={
    'param1': value1,
    'param2': value2
})
```

### 3. 查看所有可用操作

```python
from tasks.proxy import list_operations

# 获取所有操作列表
result = list_operations.delay()
operations_info = result.get()

print(operations_info)
# 输出：
# {
#     'success': True,
#     'operations': {
#         'registry': {  # 装饰器注册的操作
#             'patient.create': 'business.patient_business.create_patient',
#             'patient.get_by_id': 'business.patient_business.get_patient_by_id'
#         },
#         'manual': {    # 手动注册的操作（向后兼容）
#             'user.login': 'business.user_business.login'
#         },
#         'all': {       # 所有操作的合并
#             'user.login': 'business.user_business.login',
#             'patient.create': 'business.patient_business.create_patient'
#         }
#     },
#     'total_count': 2
# }
```

## 实际示例

### 患者管理操作

```python
# 创建患者
from tasks.proxy import execute

result = execute.delay('patient.create', kwargs={
    'patient_data': {
        'name': '张三',
        'birth_date': '1990-01-01',
        'id_card': '123456789012345678',
        'gender': '男',
        'phone': '***********'
    }
})

print(result.get())
# 输出：{'success': True, 'message': '患者创建成功', 'patient_id': 1}

# 获取患者信息
result = execute.delay('patient.get_by_id', args=[1])
print(result.get())

# 搜索患者
result = execute.delay('patient.search', kwargs={
    'keyword': '张三',
    'page': 1,
    'page_size': 10
})
print(result.get())
```

## 优势

1. **分散管理**：每个业务模块管理自己的操作注册
2. **向后兼容**：现有的手动注册方式仍然有效
3. **优先级机制**：装饰器注册优先于手动注册
4. **自动发现**：无需修改中央映射表
5. **类型安全**：装饰器提供更好的代码提示

## 迁移指南

### 从手动注册迁移到装饰器注册

1. **保持现有代码运行**：不需要立即修改现有的手动注册
2. **新业务使用装饰器**：新增的业务操作使用 `@register_operation` 装饰器
3. **逐步迁移旧操作**：可以逐步将旧的手动注册迁移到装饰器方式

```python
# 旧方式（在 tasks/proxy.py 中）
MANUAL_OPERATION_MAP = {
    'user.login': user_business.login,  # 可以保留
}

# 新方式（在业务模块中）
@register_operation('user.new_operation')
def new_operation(self, data):
    # 新的业务逻辑
    pass
```

## 注意事项

1. **导入顺序**：确保业务模块在使用前被导入
2. **实例创建**：必须创建业务类的实例才能完成注册
3. **命名规范**：建议使用 `模块名.操作名` 的格式
4. **错误处理**：业务方法应该返回统一的结果格式（包含 success 字段）
5. **会话管理**：注意数据库会话的正确关闭

## 调试技巧

```python
# 检查操作是否注册成功
from tasks.registry import operation_registry

print("已注册的操作：")
for name, func in operation_registry._operations.items():
    print(f"  {name} -> {func}")

# 检查特定操作是否存在
if operation_registry.has_operation('patient.create'):
    print("patient.create 操作已注册")
else:
    print("patient.create 操作未注册")
```

这样，您就可以在每个业务模块中独立管理操作注册，不再需要修改中央的映射表了。
