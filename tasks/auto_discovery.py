"""
自动发现和注册业务操作
"""
import os
import importlib
import inspect
import logging
from typing import Dict, Callable, Any

logger = logging.getLogger(__name__)


class AutoDiscovery:
    """自动发现业务操作"""
    
    def __init__(self, business_package: str = "business"):
        self.business_package = business_package
        self._operations: Dict[str, Callable] = {}
    
    def discover_operations(self) -> Dict[str, Callable]:
        """
        自动发现所有业务模块中的操作
        
        规则：
        1. 扫描business包下的所有模块
        2. 查找以_business结尾的类
        3. 将类中的公共方法注册为操作
        4. 操作名格式：模块名.方法名
        """
        try:
            # 获取business包的路径
            business_module = importlib.import_module(self.business_package)
            business_path = os.path.dirname(business_module.__file__)
            
            # 扫描所有Python文件
            for filename in os.listdir(business_path):
                if filename.endswith('.py') and not filename.startswith('__'):
                    module_name = filename[:-3]  # 去掉.py后缀
                    self._discover_module_operations(module_name)
                    
        except Exception as e:
            logger.error(f"自动发现操作时发生错误: {e}")
        
        return self._operations
    
    def _discover_module_operations(self, module_name: str):
        """发现单个模块中的操作"""
        try:
            # 导入模块
            full_module_name = f"{self.business_package}.{module_name}"
            module = importlib.import_module(full_module_name)
            
            # 查找business类
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    name.endswith('_business') and 
                    hasattr(module, name.lower())):  # 确保有实例
                    
                    # 获取类的实例
                    instance = getattr(module, name.lower())
                    self._register_class_methods(module_name, instance)
                    
        except Exception as e:
            logger.error(f"发现模块 {module_name} 的操作时发生错误: {e}")
    
    def _register_class_methods(self, module_name: str, instance: Any):
        """注册类实例的方法"""
        # 获取类的所有公共方法
        for method_name in dir(instance):
            if not method_name.startswith('_'):  # 排除私有方法
                method = getattr(instance, method_name)
                if callable(method):
                    # 构造操作名：模块名.方法名
                    operation_name = f"{module_name}.{method_name}"
                    self._operations[operation_name] = method
                    logger.info(f"自动注册操作: {operation_name}")


# 全局自动发现实例
auto_discovery = AutoDiscovery()


def get_auto_discovered_operations() -> Dict[str, Callable]:
    """获取自动发现的操作"""
    return auto_discovery.discover_operations()
