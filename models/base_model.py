from sqlalchemy import Column, DateTime, func
from sqlalchemy.orm import declared_attr, declarative_base, sessionmaker, scoped_session


class BaseModel:
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()

    created_time = Column(DateTime, default=func.now(), comment="创建时间")
    updated_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")


# 创建Base对象，作为其他模型依赖的基础
Base = declarative_base(cls=BaseModel)

# 从db.mysql模块导入engine
from db.mysql import engine

# 创建Session工厂，配置连接池和事务参数
SessionLocal = sessionmaker(
    autocommit=False,  # 是否自动提交事务
    autoflush=False,  # 是否自动刷新
    bind=engine,  # 绑定到特定的数据库引擎

    # 以下参数用于优化性能和资源管理
    pool_size=20,  # 连接池的大小
    max_overflow=10,  # 允许的最大溢出连接数
    pool_recycle=3600,  # 连接的最大回收时间
    pool_pre_ping=True,  # 在使用连接之前发送一个预查询以检查连接是否仍然有效
    pool_timeout=30,  # 获取连接的超时时间
    expire_on_commit=False  # 防止在事务提交后对象过期
)

# 创建线程安全的Session对象
Session = scoped_session(SessionLocal)


# 数据库会话上下文管理器
def get_db():
    """获取数据库会话的上下文管理器"""
    db = Session()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


# 创建所有的表
def init_db():
    Base.metadata.create_all(bind=engine)
