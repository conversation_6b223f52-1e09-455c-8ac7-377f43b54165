"""
患者业务逻辑模块 - 展示如何使用装饰器注册操作
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, date

from tasks.registry import register_operation
from models import Session, Patients, PatientChronicDiseases, Regions, DiseaseTypes

logger = logging.getLogger(__name__)


class PatientBusiness:
    """患者业务逻辑类"""
    
    def __init__(self):
        self.session = Session()
    
    @register_operation('patient.create')
    def create_patient(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建患者
        
        Args:
            patient_data: 患者信息字典
            
        Returns:
            创建结果
        """
        try:
            # 验证必填字段
            required_fields = ['name', 'birth_date', 'id_card']
            for field in required_fields:
                if field not in patient_data:
                    return {
                        'success': False,
                        'message': f'缺少必填字段: {field}'
                    }
            
            # 检查身份证号是否已存在
            existing_patient = self.session.query(Patients).filter_by(
                id_card=patient_data['id_card']
            ).first()
            
            if existing_patient:
                return {
                    'success': False,
                    'message': '身份证号已存在'
                }
            
            # 创建患者对象
            patient = Patients(**patient_data)
            self.session.add(patient)
            self.session.commit()
            
            return {
                'success': True,
                'message': '患者创建成功',
                'patient_id': patient.id
            }
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"创建患者时发生错误: {e}")
            return {
                'success': False,
                'message': f'创建患者失败: {str(e)}'
            }
    
    @register_operation('patient.get_by_id')
    def get_patient_by_id(self, patient_id: int) -> Dict[str, Any]:
        """根据ID获取患者信息"""
        try:
            patient = self.session.query(Patients).filter_by(
                id=patient_id, is_active=True
            ).first()
            
            if not patient:
                return {
                    'success': False,
                    'message': '患者不存在'
                }
            
            return {
                'success': True,
                'patient': {
                    'id': patient.id,
                    'name': patient.name,
                    'gender': patient.gender,
                    'birth_date': patient.birth_date.isoformat() if patient.birth_date else None,
                    'id_card': patient.id_card,
                    'phone': patient.phone,
                    'address': patient.address
                }
            }
            
        except Exception as e:
            logger.error(f"获取患者信息时发生错误: {e}")
            return {
                'success': False,
                'message': f'获取患者信息失败: {str(e)}'
            }
    
    @register_operation('patient.search')
    def search_patients(self, keyword: str = '', page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """搜索患者"""
        try:
            query = self.session.query(Patients).filter_by(is_active=True)
            
            if keyword:
                query = query.filter(
                    (Patients.name.like(f'%{keyword}%')) |
                    (Patients.id_card.like(f'%{keyword}%')) |
                    (Patients.phone.like(f'%{keyword}%'))
                )
            
            # 分页
            offset = (page - 1) * page_size
            total = query.count()
            patients = query.offset(offset).limit(page_size).all()
            
            patient_list = []
            for patient in patients:
                patient_list.append({
                    'id': patient.id,
                    'name': patient.name,
                    'gender': patient.gender,
                    'birth_date': patient.birth_date.isoformat() if patient.birth_date else None,
                    'id_card': patient.id_card,
                    'phone': patient.phone
                })
            
            return {
                'success': True,
                'patients': patient_list,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            logger.error(f"搜索患者时发生错误: {e}")
            return {
                'success': False,
                'message': f'搜索患者失败: {str(e)}'
            }
    
    @register_operation('patient.add_chronic_disease')
    def add_chronic_disease(self, patient_id: int, disease_data: Dict[str, Any]) -> Dict[str, Any]:
        """为患者添加慢性病"""
        try:
            # 验证患者是否存在
            patient = self.session.query(Patients).filter_by(
                id=patient_id, is_active=True
            ).first()
            
            if not patient:
                return {
                    'success': False,
                    'message': '患者不存在'
                }
            
            # 创建慢性病记录
            chronic_disease = PatientChronicDiseases(
                patient_id=patient_id,
                **disease_data
            )
            
            self.session.add(chronic_disease)
            self.session.commit()
            
            return {
                'success': True,
                'message': '慢性病添加成功',
                'chronic_disease_id': chronic_disease.id
            }
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"添加慢性病时发生错误: {e}")
            return {
                'success': False,
                'message': f'添加慢性病失败: {str(e)}'
            }
    
    def __del__(self):
        """析构函数，确保会话关闭"""
        if hasattr(self, 'session'):
            self.session.close()


# 创建业务实例
patient_business = PatientBusiness()
