"""
增强版代理任务 - 支持多种操作发现机制
"""
import logging
from typing import Dict, Callable, Any, Optional

from celery_task.celery_config import app
from .registry import operation_registry
from .auto_discovery import get_auto_discovered_operations

logger = logging.getLogger(__name__)


class EnhancedProxy:
    """增强版代理器"""
    
    def __init__(self):
        self._manual_operations: Dict[str, Callable] = {}
        self._auto_operations: Dict[str, Callable] = {}
        self._registry_operations: Dict[str, Callable] = {}
        self._initialized = False
    
    def _initialize(self):
        """延迟初始化，避免循环导入"""
        if self._initialized:
            return
        
        try:
            # 1. 获取装饰器注册的操作
            self._registry_operations = operation_registry._operations.copy()
            
            # 2. 获取自动发现的操作
            self._auto_operations = get_auto_discovered_operations()
            
            # 3. 手动注册的操作（向后兼容）
            self._load_manual_operations()
            
            self._initialized = True
            logger.info(f"代理器初始化完成，共注册 {len(self.get_all_operations())} 个操作")
            
        except Exception as e:
            logger.error(f"代理器初始化失败: {e}")
    
    def _load_manual_operations(self):
        """加载手动注册的操作（向后兼容）"""
        try:
            from business.user_business import user_business
            
            # 手动注册的操作，用于向后兼容
            self._manual_operations = {
                'user.login': user_business.login,
                'user.register': user_business.register,
                'user.get_roles': user_business.get_user_roles,
                'user.get_permissions': user_business.get_user_permissions,
                'user.change_roles': user_business.change_user_roles,
                'user.check_permission': user_business.check_permission,
                'user.test1': user_business.test1_business,
            }
        except Exception as e:
            logger.error(f"加载手动操作时发生错误: {e}")
    
    def get_operation(self, operation_name: str) -> Optional[Callable]:
        """
        获取操作函数，按优先级查找：
        1. 装饰器注册的操作（最高优先级）
        2. 手动注册的操作（中等优先级）
        3. 自动发现的操作（最低优先级）
        """
        self._initialize()
        
        # 按优先级查找
        for operations in [self._registry_operations, self._manual_operations, self._auto_operations]:
            if operation_name in operations:
                return operations[operation_name]
        
        return None
    
    def get_all_operations(self) -> Dict[str, Callable]:
        """获取所有操作，合并所有来源"""
        self._initialize()
        
        all_operations = {}
        # 按优先级合并，后面的会覆盖前面的
        all_operations.update(self._auto_operations)
        all_operations.update(self._manual_operations)
        all_operations.update(self._registry_operations)
        
        return all_operations
    
    def list_operations_by_source(self) -> Dict[str, Dict[str, str]]:
        """按来源列出所有操作"""
        self._initialize()
        
        def format_operations(ops: Dict[str, Callable]) -> Dict[str, str]:
            return {
                name: f"{func.__module__}.{func.__name__}" 
                for name, func in ops.items()
            }
        
        return {
            'registry': format_operations(self._registry_operations),
            'manual': format_operations(self._manual_operations),
            'auto_discovery': format_operations(self._auto_operations)
        }


# 全局代理器实例
enhanced_proxy = EnhancedProxy()


@app.task(name="yongzhou.enhanced_proxy.execute")
def execute(operation: str, args: list = None, kwargs: dict = None) -> Dict[str, Any]:
    """
    增强版统一代理任务执行器
    
    Args:
        operation: 操作路径，如 'user.login', 'user.register' 等
        args: 位置参数列表
        kwargs: 关键字参数字典
        
    Returns:
        任务执行结果
    """
    if args is None:
        args = []
    if kwargs is None:
        kwargs = {}

    try:
        # 获取业务函数
        business_func = enhanced_proxy.get_operation(operation)

        if not business_func:
            return {
                'success': False,
                'message': f'未找到操作: {operation}',
                'operation': operation,
                'available_operations': list(enhanced_proxy.get_all_operations().keys())
            }

        # 调用业务函数
        result = business_func(*args, **kwargs)
        return result

    except Exception as e:
        error_msg = f"执行操作 {operation} 时发生错误: {str(e)}"
        logger.error(error_msg)
        return {
            'success': False,
            'message': error_msg,
            'operation': operation
        }


@app.task(name="yongzhou.enhanced_proxy.list_operations")
def list_operations() -> Dict[str, Any]:
    """列出所有可用的操作"""
    try:
        return {
            'success': True,
            'operations_by_source': enhanced_proxy.list_operations_by_source(),
            'all_operations': list(enhanced_proxy.get_all_operations().keys())
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'获取操作列表时发生错误: {str(e)}'
        }
