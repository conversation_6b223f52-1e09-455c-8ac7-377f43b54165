from sqlalchemy import Column, Integer, String, Boolean
from models.base_model import Base


class ManagementStatus(Base):
    """管理状态字典表"""
    __tablename__ = 'management_status'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    status_code = Column(String(20), unique=True, nullable=False, comment='状态编码')
    status_name = Column(String(50), nullable=False, comment='状态名称')
    is_active = Column(Boolean, default=True, comment='是否启用')
    
    def __repr__(self):
        return f"<ManagementStatus(id={self.id}, status_code='{self.status_code}', status_name='{self.status_name}')>"
