from sqlalchemy import Column, Integer, String, Boolean
from models.base_model import Base


class Regions(Base):
    """地区字典表"""

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    province = Column(String(50), nullable=False, comment='省份')
    city = Column(String(50), nullable=False, comment='城市')
    county_district = Column(String(100), nullable=False, comment='县区')
    region_code = Column(String(20), comment='地区编码')
    is_active = Column(Boolean, default=True, comment='是否启用')

    def __repr__(self):
        return f"<Regions(id={self.id}, province='{self.province}', city='{self.city}', county_district='{self.county_district}')>"
