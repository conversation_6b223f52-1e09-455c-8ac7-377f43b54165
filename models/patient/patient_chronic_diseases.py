from sqlalchemy import Column, Integer, Date, <PERSON><PERSON><PERSON>, TIMESTAMP, String, Enum, ForeignKey, text
from sqlalchemy.orm import relationship
from models.base_model import Base


class PatientChronicDiseases(Base):
    """患者慢性病信息表"""
    __tablename__ = 'patient_chronic_diseases'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    patient_id = Column(Integer, ForeignKey('patients.id'), nullable=False, comment='患者ID')
    disease_type_id = Column(Integer, ForeignKey('disease_types.id'), nullable=False, comment='疾病类型ID')
    visit_date = Column(Date, comment='就诊时间')
    management_status_id = Column(Integer, ForeignKey('management_status.id'), comment='管理状态ID')
    responsible_doctor = Column(String(50), comment='责任医生')
    severity_level = Column(Enum('轻度', '中度', '重度'), comment='疾病严重程度')
    is_active = Column(<PERSON><PERSON><PERSON>, default=True, comment='是否在管')
    created_time = Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间')
    updated_time = Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间')
    
    # 关联关系
    patient = relationship("Patients", back_populates="chronic_diseases")
    disease_type = relationship("DiseaseTypes", backref="patient_diseases")
    management_status = relationship("ManagementStatus", backref="patient_diseases")
    
    def __repr__(self):
        return f"<PatientChronicDiseases(id={self.id}, patient_id={self.patient_id}, disease_type_id={self.disease_type_id})>"
