from sqlalchemy import Column, Integer, Date, TIMESTAMP, String, Text, ForeignKey, text
from sqlalchemy.orm import relationship
from models.base_model import Base


class DiagnosisRecords(Base):
    """诊断记录表"""
    __tablename__ = 'diagnosis_records'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    patient_id = Column(Integer, ForeignKey('patients.id'), nullable=False, comment='患者ID')
    visit_date = Column(Date, nullable=False, comment='就诊日期')
    diagnosis_type = Column(String(100), comment='诊断类型')
    diagnosis_result = Column(Text, comment='诊断结果')
    attending_doctor = Column(String(50), comment='主治医生')
    treatment_plan = Column(Text, comment='治疗方案')
    created_time = Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间')
    
    # 关联关系
    patient = relationship("Patients", back_populates="diagnosis_records")
    
    def __repr__(self):
        return f"<DiagnosisRecords(id={self.id}, patient_id={self.patient_id}, visit_date='{self.visit_date}')>"
