from sqlalchemy import Column, Integer, String, Bo<PERSON>an, TIMESTAMP, text
from models.base_model import Base


class DiseaseTypes(Base):
    """疾病类型字典表"""
    __tablename__ = 'disease_types'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    disease_code = Column(String(20), unique=True, nullable=False, comment='疾病编码')
    disease_name = Column(String(100), nullable=False, comment='疾病名称')
    category = Column(String(100), nullable=False, comment='疾病大类')
    icd10_code = Column(String(20), comment='ICD-10编码')
    is_active = Column(Boolean, default=True, comment='是否启用')
    created_time = Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间')
    
    def __repr__(self):
        return f"<DiseaseTypes(id={self.id}, disease_code='{self.disease_code}', disease_name='{self.disease_name}')>"
